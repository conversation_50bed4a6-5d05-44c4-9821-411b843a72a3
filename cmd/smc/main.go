package main

import (
	"fmt"
	"math"
	"time"

	"github.com/adshao/go-binance/v2/futures/smc"
)

// generateSampleData creates realistic OHLC data for demonstration
func generateSampleData() *smc.DataFrame {
	// Simulate Bitcoin price data starting from $50,000
	basePrice := 50000.0
	data := make([]smc.OHLC, 50)

	for i := 0; i < 50; i++ {
		// Create realistic price movement
		open := basePrice
		if i > 0 {
			open = data[i-1].Close
		}

		// Simulate market phases
		var priceChange float64
		switch {
		case i < 10: // Uptrend
			priceChange = float64(i)*20 + float64(i%3)*100
		case i < 20: // Correction
			priceChange = -float64(i-10)*30 + float64(i%2)*50
		case i < 30: // Recovery
			priceChange = float64(i-20)*40 + float64(i%4)*80
		default: // Continuation
			priceChange = float64(i-30)*25 + float64(i%3)*60
		}

		close := open + priceChange
		high := math.Max(open, close) + float64(i%5)*30 + 100
		low := math.Min(open, close) - float64(i%3)*25 - 50
		volume := 1000000 + float64(i%10)*200000

		data[i] = smc.OHLC{
			Open:   open,
			High:   high,
			Low:    low,
			Close:  close,
			Volume: volume,
			Time:   time.Now().Add(time.Duration(i) * time.Hour),
		}
	}

	return &smc.DataFrame{Data: data}
}

func countNonNaN(data []float64) int {
	count := 0
	for _, val := range data {
		if !math.IsNaN(val) {
			count++
		}
	}
	return count
}

func main() {
	fmt.Println("=== Smart Money Concepts (SMC) Library Demo ===")
	fmt.Println()

	// Initialize SMC analyzer
	analyzer := smc.NewSMC()

	// Generate sample market data
	fmt.Println("📊 Generating sample market data (50 candles)...")
	ohlc := generateSampleData()

	fmt.Printf("Price range: $%.2f - $%.2f\n",
		ohlc.Data[0].Close, ohlc.Data[len(ohlc.Data)-1].Close)
	fmt.Println()

	// 1. Fair Value Gap Analysis
	fmt.Println("🔍 Analyzing Fair Value Gaps (FVG)...")
	fvg, err := analyzer.FVG(ohlc, false)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fvgData := fvg.GetFloat64Column("FVG")
	bullishFVG := 0
	bearishFVG := 0
	for _, val := range fvgData {
		if !math.IsNaN(val) {
			if val > 0 {
				bullishFVG++
			} else {
				bearishFVG++
			}
		}
	}
	fmt.Printf("   ✅ Found %d Bullish FVGs and %d Bearish FVGs\n", bullishFVG, bearishFVG)

	// 2. Swing Highs and Lows
	fmt.Println("📈 Identifying Swing Highs and Lows...")
	swings, err := analyzer.SwingHighsLows(ohlc, 5)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	swingData := swings.GetFloat64Column("HighLow")
	swingCount := countNonNaN(swingData)
	fmt.Printf("   ✅ Found %d significant swing points\n", swingCount)

	// 3. Market Structure Analysis (BOS/CHoCH)
	fmt.Println("🏗️  Analyzing Market Structure Changes...")
	structure, err := analyzer.BosChoch(ohlc, swings, true)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	bosData := structure.GetFloat64Column("BOS")
	chochData := structure.GetFloat64Column("CHOCH")
	bosCount := countNonNaN(bosData)
	chochCount := countNonNaN(chochData)
	fmt.Printf("   ✅ Found %d Break of Structure (BOS) signals\n", bosCount)
	fmt.Printf("   ✅ Found %d Change of Character (CHoCH) signals\n", chochCount)

	// 4. Order Block Detection
	fmt.Println("📦 Detecting Order Blocks...")
	orderBlocks, err := analyzer.OB(ohlc, swings, false)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	obData := orderBlocks.GetFloat64Column("OB")
	obCount := countNonNaN(obData)
	fmt.Printf("   ✅ Found %d Order Blocks\n", obCount)

	// 5. Liquidity Analysis
	fmt.Println("💧 Analyzing Liquidity Zones...")
	liquidity, err := analyzer.Liquidity(ohlc, swings, 0.01)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	liqData := liquidity.GetFloat64Column("Liquidity")
	liqCount := countNonNaN(liqData)
	fmt.Printf("   ✅ Found %d Liquidity zones\n", liqCount)

	// 6. Trading Session Analysis
	fmt.Println("🌍 Analyzing Trading Sessions...")
	london, _ := analyzer.Sessions(ohlc, "London", "", "", "UTC")
	newyork, _ := analyzer.Sessions(ohlc, "New York", "", "", "UTC")

	londonActive := london.GetIntColumn("Active")
	nyActive := newyork.GetIntColumn("Active")

	londonCount := 0
	nyCount := 0
	for i := range londonActive {
		if londonActive[i] == 1 {
			londonCount++
		}
		if nyActive[i] == 1 {
			nyCount++
		}
	}

	fmt.Printf("   🇬🇧 London session: %d active periods\n", londonCount)
	fmt.Printf("   🇺🇸 New York session: %d active periods\n", nyCount)

	// 7. Retracement Analysis
	fmt.Println("📊 Calculating Retracements...")
	retracements, err := analyzer.Retracements(ohlc, swings)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	retData := retracements.GetIntColumn("Direction")
	retCount := 0
	for _, val := range retData {
		if val != 0 {
			retCount++
		}
	}
	fmt.Printf("   ✅ Calculated retracements for %d periods\n", retCount)

	fmt.Println()
	fmt.Println("🎯 SMC Analysis Complete!")
	fmt.Println("=====================================")
	fmt.Printf("📈 Total signals detected: %d\n",
		bullishFVG+bearishFVG+swingCount+bosCount+chochCount+obCount+liqCount)
	fmt.Printf("💹 Market structure: %s\n",
		func() string {
			if bosCount > 0 {
				return "Structure Break Detected"
			} else if chochCount > 0 {
				return "Character Change Detected"
			} else {
				return "Continuation Pattern"
			}
		}())
	fmt.Printf("🎲 FVG Status: %d gaps identified\n", bullishFVG+bearishFVG)
	fmt.Printf("📦 Order Blocks: %d institutional levels\n", obCount)

	fmt.Println()
	fmt.Println("✨ SMC analysis provides insights into:")
	fmt.Println("   • Smart money footprints (Order Blocks)")
	fmt.Println("   • Market inefficiencies (Fair Value Gaps)")
	fmt.Println("   • Structural changes (BOS/CHoCH)")
	fmt.Println("   • Session-based analysis")
	fmt.Println("   • Liquidity zones")
	fmt.Println("   • Price retracement levels")
}
