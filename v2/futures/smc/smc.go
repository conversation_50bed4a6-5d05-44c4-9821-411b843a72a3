package smc

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
)

// Version of the SMC library
const Version = "0.0.26"

// OHLC represents a single candlestick with Open, High, Low, Close, Volume data
type OHLC struct {
	Open   float64
	High   float64
	Low    float64
	Close  float64
	Volume float64
	Time   time.Time
}

// DataFrame represents a collection of OHLC data
type DataFrame struct {
	Data []OHLC
}

// Series represents a result series with multiple columns
type Series struct {
	Columns map[string][]interface{}
	Length  int
}

// NewSeries creates a new Series with the given length
func NewSeries(length int) *Series {
	return &Series{
		Columns: make(map[string][]interface{}),
		Length:  length,
	}
}

// AddColumn adds a column to the series
func (s *Series) AddColumn(name string, data []interface{}) {
	if len(data) != s.Length {
		panic(fmt.Sprintf("Column %s length %d does not match series length %d", name, len(data), s.Length))
	}
	s.Columns[name] = data
}

// GetFloat64Column returns a float64 column, converting from interface{} if needed
func (s *Series) GetFloat64Column(name string) []float64 {
	col, exists := s.Columns[name]
	if !exists {
		return nil
	}

	result := make([]float64, len(col))
	for i, v := range col {
		switch val := v.(type) {
		case float64:
			result[i] = val
		case int:
			result[i] = float64(val)
		case int32:
			result[i] = float64(val)
		case int64:
			result[i] = float64(val)
		default:
			if math.IsNaN(val.(float64)) {
				result[i] = math.NaN()
			} else {
				result[i] = val.(float64)
			}
		}
	}
	return result
}

// GetIntColumn returns an int column, converting from interface{} if needed
func (s *Series) GetIntColumn(name string) []int {
	col, exists := s.Columns[name]
	if !exists {
		return nil
	}

	result := make([]int, len(col))
	for i, v := range col {
		switch val := v.(type) {
		case int:
			result[i] = val
		case int32:
			result[i] = int(val)
		case int64:
			result[i] = int(val)
		case float64:
			if math.IsNaN(val) {
				result[i] = 0
			} else {
				result[i] = int(val)
			}
		default:
			result[i] = val.(int)
		}
	}
	return result
}

// SMC is the main struct containing all Smart Money Concepts methods
type SMC struct{}

// NewSMC creates a new SMC instance
func NewSMC() *SMC {
	return &SMC{}
}

// validateOHLC validates that the DataFrame has the required OHLC columns
func validateOHLC(df *DataFrame) error {
	if df == nil || len(df.Data) == 0 {
		return fmt.Errorf("DataFrame is empty or nil")
	}
	return nil
}

// shift creates a shifted version of a float64 slice to match pandas behavior
// shift(1) means current[i] = previous[i-1] (introduce NaN at beginning)
// shift(-1) means current[i] = next[i+1] (introduce NaN at end)
func shift(data []float64, periods int) []float64 {
	result := make([]float64, len(data))

	if periods > 0 {
		// Shift forward: current[i] = previous[i-periods]
		for i := 0; i < len(data); i++ {
			if i-periods >= 0 {
				result[i] = data[i-periods]
			} else {
				result[i] = math.NaN()
			}
		}
	} else if periods < 0 {
		// Shift backward: current[i] = next[i-periods]
		periods = -periods
		for i := 0; i < len(data); i++ {
			if i+periods < len(data) {
				result[i] = data[i+periods]
			} else {
				result[i] = math.NaN()
			}
		}
	} else {
		// No shift
		copy(result, data)
	}

	return result
}

// rolling calculates rolling window statistics
func rolling(data []float64, window int, fn func([]float64) float64) []float64 {
	result := make([]float64, len(data))

	for i := 0; i < len(data); i++ {
		if i < window-1 {
			result[i] = math.NaN()
		} else {
			windowData := data[i-window+1 : i+1]
			result[i] = fn(windowData)
		}
	}

	return result
}

// max returns the maximum value in a slice
func max(data []float64) float64 {
	if len(data) == 0 {
		return math.NaN()
	}

	maxVal := data[0]
	for _, v := range data[1:] {
		if !math.IsNaN(v) && (math.IsNaN(maxVal) || v > maxVal) {
			maxVal = v
		}
	}
	return maxVal
}

// min returns the minimum value in a slice
func min(data []float64) float64 {
	if len(data) == 0 {
		return math.NaN()
	}

	minVal := data[0]
	for _, v := range data[1:] {
		if !math.IsNaN(v) && (math.IsNaN(minVal) || v < minVal) {
			minVal = v
		}
	}
	return minVal
}

// extractOHLCArrays extracts OHLC data into separate arrays
func extractOHLCArrays(df *DataFrame) ([]float64, []float64, []float64, []float64, []float64) {
	length := len(df.Data)
	open := make([]float64, length)
	high := make([]float64, length)
	low := make([]float64, length)
	close := make([]float64, length)
	volume := make([]float64, length)

	for i, ohlc := range df.Data {
		open[i] = ohlc.Open
		high[i] = ohlc.High
		low[i] = ohlc.Low
		close[i] = ohlc.Close
		volume[i] = ohlc.Volume
	}

	return open, high, low, close, volume
}

// toInterfaceSlice converts various slice types to []interface{}
func toInterfaceSlice(data interface{}) []interface{} {
	switch v := data.(type) {
	case []float64:
		result := make([]interface{}, len(v))
		for i, val := range v {
			result[i] = val
		}
		return result
	case []int:
		result := make([]interface{}, len(v))
		for i, val := range v {
			result[i] = val
		}
		return result
	case []int32:
		result := make([]interface{}, len(v))
		for i, val := range v {
			result[i] = val
		}
		return result
	default:
		panic(fmt.Sprintf("Unsupported slice type: %T", data))
	}
}

// FVG calculates Fair Value Gaps
// A fair value gap is when the previous high is lower than the next low if the current candle is bullish.
// Or when the previous low is higher than the next high if the current candle is bearish.
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - joinConsecutive: if there are multiple FVG in a row then they will be merged into one using the highest top and the lowest bottom
//
// Returns:
// - FVG = 1 if bullish fair value gap, -1 if bearish fair value gap
// - Top = the top of the fair value gap
// - Bottom = the bottom of the fair value gap
// - MitigatedIndex = the index of the candle that mitigated the fair value gap
func (smc *SMC) FVG(ohlc *DataFrame, joinConsecutive bool) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	open, high, low, close, _ := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)

	// Shift arrays for calculations - now with correct pandas-like behavior
	previousHigh := shift(high, 1) // high.shift(1) - previous high
	nextLow := shift(low, -1)      // low.shift(-1) - next low
	previousLow := shift(low, 1)   // low.shift(1) - previous low
	nextHigh := shift(high, -1)    // high.shift(-1) - next high

	fvg := make([]float64, length)
	top := make([]float64, length)
	bottom := make([]float64, length)

	for i := 0; i < length; i++ {
		// Check for bullish FVG: (high.shift(1) < low.shift(-1)) & (close > open)
		// Check for bearish FVG: (low.shift(1) > high.shift(-1)) & (close < open)
		bullishCondition := !math.IsNaN(previousHigh[i]) && !math.IsNaN(nextLow[i]) &&
			previousHigh[i] < nextLow[i] && close[i] > open[i]
		bearishCondition := !math.IsNaN(previousLow[i]) && !math.IsNaN(nextHigh[i]) &&
			previousLow[i] > nextHigh[i] && close[i] < open[i]

		if bullishCondition {
			fvg[i] = 1
			top[i] = nextLow[i]         // low.shift(-1) for bullish
			bottom[i] = previousHigh[i] // high.shift(1) for bullish
		} else if bearishCondition {
			fvg[i] = -1
			top[i] = previousLow[i] // low.shift(1) for bearish
			bottom[i] = nextHigh[i] // high.shift(-1) for bearish
		} else {
			fvg[i] = math.NaN()
			top[i] = math.NaN()
			bottom[i] = math.NaN()
		}
	}

	// Join consecutive FVGs if requested
	if joinConsecutive {
		for i := 0; i < length-1; i++ {
			if !math.IsNaN(fvg[i]) && !math.IsNaN(fvg[i+1]) && fvg[i] == fvg[i+1] {
				top[i+1] = math.Max(top[i], top[i+1])
				bottom[i+1] = math.Min(bottom[i], bottom[i+1])
				fvg[i] = math.NaN()
				top[i] = math.NaN()
				bottom[i] = math.NaN()
			}
		}
	}

	// Calculate mitigation indices
	mitigatedIndex := make([]int32, length)
	for i := 0; i < length; i++ {
		if !math.IsNaN(fvg[i]) {
			found := false
			for j := i + 2; j < length; j++ {
				if fvg[i] == 1 && low[j] <= top[i] {
					mitigatedIndex[i] = int32(j)
					found = true
					break
				} else if fvg[i] == -1 && high[j] >= bottom[i] {
					mitigatedIndex[i] = int32(j)
					found = true
					break
				}
			}
			if !found {
				mitigatedIndex[i] = 0
			}
		}
	}

	// Convert to interface slices for Series
	fvgInterface := toInterfaceSlice(fvg)
	topInterface := toInterfaceSlice(top)
	bottomInterface := toInterfaceSlice(bottom)
	mitigatedInterface := toInterfaceSlice(mitigatedIndex)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("FVG", fvgInterface)
	result.AddColumn("Top", topInterface)
	result.AddColumn("Bottom", bottomInterface)
	result.AddColumn("MitigatedIndex", mitigatedInterface)

	return result, nil
}

// SwingHighsLows calculates swing highs and lows
// A swing high is when the current high is the highest high out of the swing_length amount of candles before and after.
// A swing low is when the current low is the lowest low out of the swing_length amount of candles before and after.
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - swingLength: the amount of candles to look back and forward to determine the swing high or low
//
// Returns:
// - HighLow = 1 if swing high, -1 if swing low
// - Level = the level of the swing high or low
func (smc *SMC) SwingHighsLows(ohlc *DataFrame, swingLength int) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	_, high, low, _, _ := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)
	swingLength *= 2

	swingHighsLows := make([]float64, length)

	// Use rolling operations to match Python implementation exactly
	// high.shift(-(swing_length // 2)).rolling(swing_length).max()
	shiftedHigh := shift(high, -(swingLength / 2))
	rollingMaxHigh := rolling(shiftedHigh, swingLength, max)

	// low.shift(-(swing_length // 2)).rolling(swing_length).min()
	shiftedLow := shift(low, -(swingLength / 2))
	rollingMinLow := rolling(shiftedLow, swingLength, min)

	// Set swing highs and lows based on rolling calculations
	for i := 0; i < length; i++ {
		if !math.IsNaN(rollingMaxHigh[i]) && high[i] == rollingMaxHigh[i] {
			swingHighsLows[i] = 1
		} else if !math.IsNaN(rollingMinLow[i]) && low[i] == rollingMinLow[i] {
			swingHighsLows[i] = -1
		} else {
			swingHighsLows[i] = math.NaN()
		}
	}

	// Remove consecutive same-type swings, keeping the more extreme one
	for {
		positions := make([]int, 0)
		for i := 0; i < length; i++ {
			if !math.IsNaN(swingHighsLows[i]) {
				positions = append(positions, i)
			}
		}

		if len(positions) < 2 {
			break
		}

		indexToRemove := make([]bool, len(positions))
		changed := false

		for i := 0; i < len(positions)-1; i++ {
			current := swingHighsLows[positions[i]]
			next := swingHighsLows[positions[i+1]]

			if current == 1 && next == 1 {
				// Consecutive highs - keep the higher one
				if high[positions[i]] < high[positions[i+1]] {
					indexToRemove[i] = true
				} else {
					indexToRemove[i+1] = true
				}
				changed = true
			} else if current == -1 && next == -1 {
				// Consecutive lows - keep the lower one
				if low[positions[i]] > low[positions[i+1]] {
					indexToRemove[i] = true
				} else {
					indexToRemove[i+1] = true
				}
				changed = true
			}
		}

		if !changed {
			break
		}

		// Remove marked positions
		for i := 0; i < len(positions); i++ {
			if indexToRemove[i] {
				swingHighsLows[positions[i]] = math.NaN()
			}
		}
	}

	// Ensure alternating pattern by adding boundary conditions
	positions := make([]int, 0)
	for i := 0; i < length; i++ {
		if !math.IsNaN(swingHighsLows[i]) {
			positions = append(positions, i)
		}
	}

	if len(positions) > 0 {
		// Adjust first and last points to ensure alternating pattern
		if swingHighsLows[positions[0]] == 1 {
			swingHighsLows[0] = -1
		} else {
			swingHighsLows[0] = 1
		}

		if len(positions) > 1 {
			lastPos := positions[len(positions)-1]
			if swingHighsLows[lastPos] == -1 {
				swingHighsLows[length-1] = 1
			} else {
				swingHighsLows[length-1] = -1
			}
		}
	}

	// Calculate levels
	level := make([]float64, length)
	for i := 0; i < length; i++ {
		if !math.IsNaN(swingHighsLows[i]) {
			if swingHighsLows[i] == 1 {
				level[i] = high[i]
			} else {
				level[i] = low[i]
			}
		} else {
			level[i] = math.NaN()
		}
	}

	// Convert to interface slices for Series
	swingInterface := toInterfaceSlice(swingHighsLows)
	levelInterface := toInterfaceSlice(level)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("HighLow", swingInterface)
	result.AddColumn("Level", levelInterface)

	return result, nil
}

// BosChoch calculates Break of Structure (BOS) and Change of Character (CHoCH)
// These are both indications of market structure changing
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - swingHighsLows: DataFrame from the SwingHighsLows function
// - closeBreak: if True then the break of structure will be mitigated based on the close of the candle otherwise it will be the high/low
//
// Returns:
// - BOS = 1 if bullish break of structure, -1 if bearish break of structure
// - CHOCH = 1 if bullish change of character, -1 if bearish change of character
// - Level = the level of the break of structure or change of character
// - BrokenIndex = the index of the candle that broke the level
func (smc *SMC) BosChoch(ohlc *DataFrame, swingHighsLows *Series, closeBreak bool) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	_, high, low, close, _ := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)

	// Get swing highs and lows data
	swingHL := swingHighsLows.GetFloat64Column("HighLow")
	swingLevel := swingHighsLows.GetFloat64Column("Level")

	if swingHL == nil || swingLevel == nil {
		return nil, fmt.Errorf("swing highs lows data is missing required columns")
	}

	levelOrder := make([]float64, 0)
	highsLowsOrder := make([]float64, 0)
	lastPositions := make([]int, 0)

	bos := make([]int32, length)
	choch := make([]int32, length)
	level := make([]float64, length)

	for i := 0; i < length; i++ {
		if !math.IsNaN(swingHL[i]) {
			levelOrder = append(levelOrder, swingLevel[i])
			highsLowsOrder = append(highsLowsOrder, swingHL[i])
			lastPositions = append(lastPositions, i)

			if len(levelOrder) >= 4 {
				idx := len(lastPositions) - 3 // Index for last_positions[-2]
				if idx >= 0 && idx < len(lastPositions) {
					pos := lastPositions[idx]

					// Check patterns for the last 4 swings
					pattern := highsLowsOrder[len(highsLowsOrder)-4:]
					levels := levelOrder[len(levelOrder)-4:]

					// Bullish BOS pattern: [-1, 1, -1, 1] with ascending levels
					if len(pattern) == 4 && pattern[0] == -1 && pattern[1] == 1 && pattern[2] == -1 && pattern[3] == 1 {
						if levels[0] < levels[2] && levels[2] < levels[1] && levels[1] < levels[3] {
							bos[pos] = 1
							level[pos] = levels[1] // level_order[-3]
						}
					}

					// Bearish BOS pattern: [1, -1, 1, -1] with descending levels
					if len(pattern) == 4 && pattern[0] == 1 && pattern[1] == -1 && pattern[2] == 1 && pattern[3] == -1 {
						if levels[0] > levels[2] && levels[2] > levels[1] && levels[1] > levels[3] {
							bos[pos] = -1
							level[pos] = levels[1] // level_order[-3]
						}
					}

					// Bullish CHoCH pattern: [-1, 1, -1, 1] with different level arrangement
					if len(pattern) == 4 && pattern[0] == -1 && pattern[1] == 1 && pattern[2] == -1 && pattern[3] == 1 {
						if levels[3] > levels[1] && levels[1] > levels[0] && levels[0] > levels[2] {
							choch[pos] = 1
							level[pos] = levels[1] // level_order[-3]
						}
					}

					// Bearish CHoCH pattern: [1, -1, 1, -1] with different level arrangement
					if len(pattern) == 4 && pattern[0] == 1 && pattern[1] == -1 && pattern[2] == 1 && pattern[3] == -1 {
						if levels[3] < levels[1] && levels[1] < levels[0] && levels[0] < levels[2] {
							choch[pos] = -1
							level[pos] = levels[1] // level_order[-3]
						}
					}
				}
			}
		}
	}

	// Calculate broken indices
	broken := make([]int32, length)
	for i := 0; i < length; i++ {
		if bos[i] != 0 || choch[i] != 0 {
			found := false
			for j := i + 2; j < length; j++ {
				var condition bool
				if bos[i] == 1 || choch[i] == 1 {
					if closeBreak {
						condition = close[j] > level[i]
					} else {
						condition = high[j] > level[i]
					}
				} else if bos[i] == -1 || choch[i] == -1 {
					if closeBreak {
						condition = close[j] < level[i]
					} else {
						condition = low[j] < level[i]
					}
				}

				if condition {
					broken[i] = int32(j)
					found = true

					// Remove any unbroken BOS or CHoCH that started before this one and ended after this one
					for k := 0; k < i; k++ {
						if (bos[k] != 0 || choch[k] != 0) && broken[k] >= int32(j) {
							bos[k] = 0
							choch[k] = 0
							level[k] = 0
						}
					}
					break
				}
			}
			if !found {
				broken[i] = 0
			}
		}
	}

	// Remove unbroken ones
	for i := 0; i < length; i++ {
		if (bos[i] != 0 || choch[i] != 0) && broken[i] == 0 {
			bos[i] = 0
			choch[i] = 0
			level[i] = 0
		}
	}

	// Convert zeros to NaN
	bosFloat := make([]float64, length)
	chochFloat := make([]float64, length)
	levelFloat := make([]float64, length)
	brokenFloat := make([]float64, length)

	for i := 0; i < length; i++ {
		if bos[i] != 0 {
			bosFloat[i] = float64(bos[i])
		} else {
			bosFloat[i] = math.NaN()
		}

		if choch[i] != 0 {
			chochFloat[i] = float64(choch[i])
		} else {
			chochFloat[i] = math.NaN()
		}

		if level[i] != 0 {
			levelFloat[i] = level[i]
		} else {
			levelFloat[i] = math.NaN()
		}

		if broken[i] != 0 {
			brokenFloat[i] = float64(broken[i])
		} else {
			brokenFloat[i] = math.NaN()
		}
	}

	// Convert to interface slices for Series
	bosInterface := toInterfaceSlice(bosFloat)
	chochInterface := toInterfaceSlice(chochFloat)
	levelInterface := toInterfaceSlice(levelFloat)
	brokenInterface := toInterfaceSlice(brokenFloat)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("BOS", bosInterface)
	result.AddColumn("CHOCH", chochInterface)
	result.AddColumn("Level", levelInterface)
	result.AddColumn("BrokenIndex", brokenInterface)

	return result, nil
}

// OB calculates Order Blocks
// This method detects order blocks when there is a high amount of market orders exist on a price range.
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - swingHighsLows: DataFrame from the SwingHighsLows function
// - closeMitigation: if True then the order block will be mitigated based on the close of the candle otherwise it will be the high/low
//
// Returns:
// - OB = 1 if bullish order block, -1 if bearish order block
// - Top = top of the order block
// - Bottom = bottom of the order block
// - OBVolume = volume + 2 last volumes amounts
// - Percentage = strength of order block (min(highVolume, lowVolume)/max(highVolume, lowVolume))
func (smc *SMC) OB(ohlc *DataFrame, swingHighsLows *Series, closeMitigation bool) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	open, high, low, close, volume := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)

	// Get swing highs and lows data
	swingHL := swingHighsLows.GetFloat64Column("HighLow")
	if swingHL == nil {
		return nil, fmt.Errorf("swing highs lows data is missing HighLow column")
	}

	// Pre-allocate arrays
	crossed := make([]bool, length)
	ob := make([]int32, length)
	topArr := make([]float64, length)
	bottomArr := make([]float64, length)
	obVolume := make([]float64, length)
	lowVolume := make([]float64, length)
	highVolume := make([]float64, length)
	percentage := make([]float64, length)
	mitigatedIndex := make([]int32, length)
	breaker := make([]bool, length)

	// Get swing high and low indices
	swingHighIndices := make([]int, 0)
	swingLowIndices := make([]int, 0)
	for i := 0; i < length; i++ {
		if !math.IsNaN(swingHL[i]) {
			if swingHL[i] == 1 {
				swingHighIndices = append(swingHighIndices, i)
			} else if swingHL[i] == -1 {
				swingLowIndices = append(swingLowIndices, i)
			}
		}
	}

	// Track active bullish order blocks
	activeBullish := make([]int, 0)
	for i := 0; i < length; i++ {
		// Update existing bullish OB
		for j := len(activeBullish) - 1; j >= 0; j-- {
			idx := activeBullish[j]
			if breaker[idx] {
				if high[i] > topArr[idx] {
					// Reset this OB
					ob[idx] = 0
					topArr[idx] = 0.0
					bottomArr[idx] = 0.0
					obVolume[idx] = 0.0
					lowVolume[idx] = 0.0
					highVolume[idx] = 0.0
					mitigatedIndex[idx] = 0
					percentage[idx] = 0.0
					// Remove from active list
					activeBullish = append(activeBullish[:j], activeBullish[j+1:]...)
				}
			} else {
				var condition bool
				if closeMitigation {
					condition = math.Min(open[i], close[i]) < bottomArr[idx]
				} else {
					condition = low[i] < bottomArr[idx]
				}
				if condition {
					breaker[idx] = true
					mitigatedIndex[idx] = int32(i - 1)
				}
			}
		}

		// Find last swing high index less than current candle
		var lastTopIndex int = -1
		for _, idx := range swingHighIndices {
			if idx < i {
				lastTopIndex = idx
			} else {
				break
			}
		}

		if lastTopIndex != -1 && close[i] > high[lastTopIndex] && !crossed[lastTopIndex] {
			crossed[lastTopIndex] = true

			// Initialize with default values from previous candle
			defaultIndex := i - 1
			if defaultIndex < 0 {
				defaultIndex = 0
			}
			obBtm := high[defaultIndex]
			obTop := low[defaultIndex]
			obIndex := defaultIndex

			// Look for a lower low between lastTopIndex and current candle
			if i-lastTopIndex > 1 {
				start := lastTopIndex + 1
				end := i
				if end > start {
					minVal := low[start]
					candidateIndex := start
					for k := start + 1; k < end; k++ {
						if low[k] <= minVal {
							minVal = low[k]
							candidateIndex = k
						}
					}
					obBtm = low[candidateIndex]
					obTop = high[candidateIndex]
					obIndex = candidateIndex
				}
			}

			// Set bullish OB values
			ob[obIndex] = 1
			topArr[obIndex] = obTop
			bottomArr[obIndex] = obBtm

			volCur := volume[i]
			volPrev1 := float64(0)
			if i >= 1 {
				volPrev1 = volume[i-1]
			}
			volPrev2 := float64(0)
			if i >= 2 {
				volPrev2 = volume[i-2]
			}

			obVolume[obIndex] = volCur + volPrev1 + volPrev2
			lowVolume[obIndex] = volPrev2
			highVolume[obIndex] = volCur + volPrev1

			maxVol := math.Max(highVolume[obIndex], lowVolume[obIndex])
			if maxVol != 0 {
				percentage[obIndex] = math.Min(highVolume[obIndex], lowVolume[obIndex]) / maxVol * 100.0
			} else {
				percentage[obIndex] = 100.0
			}

			activeBullish = append(activeBullish, obIndex)
		}
	}

	// Track active bearish order blocks
	activeBearish := make([]int, 0)
	for i := 0; i < length; i++ {
		// Update existing bearish OB
		for j := len(activeBearish) - 1; j >= 0; j-- {
			idx := activeBearish[j]
			if breaker[idx] {
				if low[i] < bottomArr[idx] {
					ob[idx] = 0
					topArr[idx] = 0.0
					bottomArr[idx] = 0.0
					obVolume[idx] = 0.0
					lowVolume[idx] = 0.0
					highVolume[idx] = 0.0
					mitigatedIndex[idx] = 0
					percentage[idx] = 0.0
					// Remove from active list
					activeBearish = append(activeBearish[:j], activeBearish[j+1:]...)
				}
			} else {
				var condition bool
				if closeMitigation {
					condition = math.Max(open[i], close[i]) > topArr[idx]
				} else {
					condition = high[i] > topArr[idx]
				}
				if condition {
					breaker[idx] = true
					mitigatedIndex[idx] = int32(i)
				}
			}
		}

		// Find last swing low index less than current candle
		var lastBtmIndex int = -1
		for _, idx := range swingLowIndices {
			if idx < i {
				lastBtmIndex = idx
			} else {
				break
			}
		}

		if lastBtmIndex != -1 && close[i] < low[lastBtmIndex] && !crossed[lastBtmIndex] {
			crossed[lastBtmIndex] = true

			defaultIndex := i - 1
			if defaultIndex < 0 {
				defaultIndex = 0
			}
			obTop := high[defaultIndex]
			obBtm := low[defaultIndex]
			obIndex := defaultIndex

			if i-lastBtmIndex > 1 {
				start := lastBtmIndex + 1
				end := i
				if end > start {
					maxVal := high[start]
					candidateIndex := start
					for k := start + 1; k < end; k++ {
						if high[k] >= maxVal {
							maxVal = high[k]
							candidateIndex = k
						}
					}
					obTop = high[candidateIndex]
					obBtm = low[candidateIndex]
					obIndex = candidateIndex
				}
			}

			ob[obIndex] = -1
			topArr[obIndex] = obTop
			bottomArr[obIndex] = obBtm

			volCur := volume[i]
			volPrev1 := float64(0)
			if i >= 1 {
				volPrev1 = volume[i-1]
			}
			volPrev2 := float64(0)
			if i >= 2 {
				volPrev2 = volume[i-2]
			}

			obVolume[obIndex] = volCur + volPrev1 + volPrev2
			lowVolume[obIndex] = volCur + volPrev1
			highVolume[obIndex] = volPrev2

			maxVol := math.Max(highVolume[obIndex], lowVolume[obIndex])
			if maxVol != 0 {
				percentage[obIndex] = math.Min(highVolume[obIndex], lowVolume[obIndex]) / maxVol * 100.0
			} else {
				percentage[obIndex] = 100.0
			}

			activeBearish = append(activeBearish, obIndex)
		}
	}

	// Convert zeros to NaN where OB was not set
	obFloat := make([]float64, length)
	topFloat := make([]float64, length)
	bottomFloat := make([]float64, length)
	obVolumeFloat := make([]float64, length)
	mitigatedFloat := make([]float64, length)
	percentageFloat := make([]float64, length)

	for i := 0; i < length; i++ {
		if ob[i] != 0 {
			obFloat[i] = float64(ob[i])
			topFloat[i] = topArr[i]
			bottomFloat[i] = bottomArr[i]
			obVolumeFloat[i] = obVolume[i]
			mitigatedFloat[i] = float64(mitigatedIndex[i])
			percentageFloat[i] = percentage[i]
		} else {
			obFloat[i] = math.NaN()
			topFloat[i] = math.NaN()
			bottomFloat[i] = math.NaN()
			obVolumeFloat[i] = math.NaN()
			mitigatedFloat[i] = math.NaN()
			percentageFloat[i] = math.NaN()
		}
	}

	// Convert to interface slices for Series
	obInterface := toInterfaceSlice(obFloat)
	topInterface := toInterfaceSlice(topFloat)
	bottomInterface := toInterfaceSlice(bottomFloat)
	obVolumeInterface := toInterfaceSlice(obVolumeFloat)
	mitigatedInterface := toInterfaceSlice(mitigatedFloat)
	percentageInterface := toInterfaceSlice(percentageFloat)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("OB", obInterface)
	result.AddColumn("Top", topInterface)
	result.AddColumn("Bottom", bottomInterface)
	result.AddColumn("OBVolume", obVolumeInterface)
	result.AddColumn("MitigatedIndex", mitigatedInterface)
	result.AddColumn("Percentage", percentageInterface)

	return result, nil
}

// Liquidity calculates liquidity levels
// Liquidity is when there are multiple highs within a small range of each other,
// or multiple lows within a small range of each other.
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - swingHighsLows: DataFrame from the SwingHighsLows function
// - rangePercent: the percentage of the range to determine liquidity
//
// Returns:
// - Liquidity = 1 if bullish liquidity, -1 if bearish liquidity
// - Level = the level of the liquidity
// - End = the index of the last liquidity level
// - Swept = the index of the candle that swept the liquidity
func (smc *SMC) Liquidity(ohlc *DataFrame, swingHighsLows *Series, rangePercent float64) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	_, high, low, _, _ := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)

	// Get swing highs and lows data
	swingHL := swingHighsLows.GetFloat64Column("HighLow")
	swingLevel := swingHighsLows.GetFloat64Column("Level")

	if swingHL == nil || swingLevel == nil {
		return nil, fmt.Errorf("swing highs lows data is missing required columns")
	}

	// Calculate the pip range based on the overall high-low range
	overallHigh := max(high)
	overallLow := min(low)
	pipRange := (overallHigh - overallLow) * rangePercent

	// Make copies to allow in-place marking of used candidates
	shlHL := make([]float64, length)
	shlLevel := make([]float64, length)
	copy(shlHL, swingHL)
	copy(shlLevel, swingLevel)

	// Initialize output arrays
	liquidity := make([]float64, length)
	liquidityLevel := make([]float64, length)
	liquidityEnd := make([]float64, length)
	liquiditySwept := make([]float64, length)

	for i := 0; i < length; i++ {
		liquidity[i] = math.NaN()
		liquidityLevel[i] = math.NaN()
		liquidityEnd[i] = math.NaN()
		liquiditySwept[i] = math.NaN()
	}

	// Process bullish liquidity (HighLow == 1)
	bullIndices := make([]int, 0)
	for i := 0; i < length; i++ {
		if !math.IsNaN(shlHL[i]) && shlHL[i] == 1 {
			bullIndices = append(bullIndices, i)
		}
	}

	for _, i := range bullIndices {
		// Skip if this candidate has already been used
		if math.IsNaN(shlHL[i]) || shlHL[i] != 1 {
			continue
		}

		highLevel := shlLevel[i]
		rangeLow := highLevel - pipRange
		rangeHigh := highLevel + pipRange
		groupLevels := []float64{highLevel}
		groupEnd := i

		// Determine the swept index
		swept := 0
		for j := i + 1; j < length; j++ {
			if high[j] >= rangeHigh {
				swept = j
				break
			}
		}

		// Iterate over candidate indices greater than i
		for _, j := range bullIndices {
			if j <= i {
				continue
			}
			// If we've reached or passed the swept index, stop
			if swept != 0 && j >= swept {
				break
			}
			// If candidate j is within the liquidity range, add it and mark it as used
			if !math.IsNaN(shlHL[j]) && shlHL[j] == 1 && shlLevel[j] >= rangeLow && shlLevel[j] <= rangeHigh {
				groupLevels = append(groupLevels, shlLevel[j])
				groupEnd = j
				shlHL[j] = math.NaN() // mark candidate as used
			}
		}

		// Only record liquidity if more than one candidate is grouped
		if len(groupLevels) > 1 {
			avgLevel := 0.0
			for _, level := range groupLevels {
				avgLevel += level
			}
			avgLevel /= float64(len(groupLevels))

			liquidity[i] = 1
			liquidityLevel[i] = avgLevel
			liquidityEnd[i] = float64(groupEnd)
			liquiditySwept[i] = float64(swept)
		}
	}

	// Process bearish liquidity (HighLow == -1)
	bearIndices := make([]int, 0)
	for i := 0; i < length; i++ {
		if !math.IsNaN(shlHL[i]) && shlHL[i] == -1 {
			bearIndices = append(bearIndices, i)
		}
	}

	for _, i := range bearIndices {
		if math.IsNaN(shlHL[i]) || shlHL[i] != -1 {
			continue
		}

		lowLevel := shlLevel[i]
		rangeLow := lowLevel - pipRange
		rangeHigh := lowLevel + pipRange
		groupLevels := []float64{lowLevel}
		groupEnd := i

		// Find the first candle after i where the low reaches or goes below rangeLow
		swept := 0
		for j := i + 1; j < length; j++ {
			if low[j] <= rangeLow {
				swept = j
				break
			}
		}

		for _, j := range bearIndices {
			if j <= i {
				continue
			}
			if swept != 0 && j >= swept {
				break
			}
			if !math.IsNaN(shlHL[j]) && shlHL[j] == -1 && shlLevel[j] >= rangeLow && shlLevel[j] <= rangeHigh {
				groupLevels = append(groupLevels, shlLevel[j])
				groupEnd = j
				shlHL[j] = math.NaN()
			}
		}

		if len(groupLevels) > 1 {
			avgLevel := 0.0
			for _, level := range groupLevels {
				avgLevel += level
			}
			avgLevel /= float64(len(groupLevels))

			liquidity[i] = -1
			liquidityLevel[i] = avgLevel
			liquidityEnd[i] = float64(groupEnd)
			liquiditySwept[i] = float64(swept)
		}
	}

	// Convert to interface slices for Series
	liqInterface := toInterfaceSlice(liquidity)
	levelInterface := toInterfaceSlice(liquidityLevel)
	endInterface := toInterfaceSlice(liquidityEnd)
	sweptInterface := toInterfaceSlice(liquiditySwept)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("Liquidity", liqInterface)
	result.AddColumn("Level", levelInterface)
	result.AddColumn("End", endInterface)
	result.AddColumn("Swept", sweptInterface)

	return result, nil
}

// PreviousHighLow calculates previous high and low levels for different timeframes
// This method returns the previous high and low of the given time frame.
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - timeFrame: the time frame to get the previous high and low (15m, 1H, 4H, 1D, 1W, 1M)
//
// Returns:
// - PreviousHigh = the previous high
// - PreviousLow = the previous low
// - BrokenHigh = 1 once price has broken the previous high of the timeframe, 0 otherwise
// - BrokenLow = 1 once price has broken the previous low of the timeframe, 0 otherwise
func (smc *SMC) PreviousHighLow(ohlc *DataFrame, timeFrame string) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	_, high, low, _, _ := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)

	// For simplicity, we'll implement a basic version that works with the assumption
	// that the data is already in the desired timeframe or we calculate based on periods
	// In a real implementation, you'd need proper time-based resampling

	previousHigh := make([]float64, length)
	previousLow := make([]float64, length)
	brokenHigh := make([]int, length)
	brokenLow := make([]int, length)

	// Simple implementation: use a rolling window approach
	// This is a simplified version - in practice you'd need proper timeframe handling
	var windowSize int
	switch timeFrame {
	case "15m":
		windowSize = 15
	case "1H":
		windowSize = 60
	case "4H":
		windowSize = 240
	case "1D":
		windowSize = 1440
	case "1W":
		windowSize = 10080
	case "1M":
		windowSize = 43200
	default:
		windowSize = 1440 // Default to 1D
	}

	// Adjust window size based on data frequency (this is simplified)
	if windowSize > length {
		windowSize = length / 4
	}
	if windowSize < 2 {
		windowSize = 2
	}

	currentlyBrokenHigh := false
	currentlyBrokenLow := false
	lastBrokenTime := -1

	for i := 0; i < length; i++ {
		if i < windowSize {
			previousHigh[i] = math.NaN()
			previousLow[i] = math.NaN()
			brokenHigh[i] = 0
			brokenLow[i] = 0
			continue
		}

		// Calculate previous period high/low
		start := i - windowSize
		if start < 0 {
			start = 0
		}

		periodHigh := max(high[start:i])
		periodLow := min(low[start:i])

		// Reset broken status for new period
		if lastBrokenTime != start {
			currentlyBrokenHigh = false
			currentlyBrokenLow = false
			lastBrokenTime = start
		}

		previousHigh[i] = periodHigh
		previousLow[i] = periodLow

		// Check if current high/low breaks previous levels
		currentlyBrokenHigh = high[i] > periodHigh || currentlyBrokenHigh
		currentlyBrokenLow = low[i] < periodLow || currentlyBrokenLow

		if currentlyBrokenHigh {
			brokenHigh[i] = 1
		} else {
			brokenHigh[i] = 0
		}

		if currentlyBrokenLow {
			brokenLow[i] = 1
		} else {
			brokenLow[i] = 0
		}
	}

	// Convert to interface slices for Series
	prevHighInterface := toInterfaceSlice(previousHigh)
	prevLowInterface := toInterfaceSlice(previousLow)
	brokenHighInterface := toInterfaceSlice(brokenHigh)
	brokenLowInterface := toInterfaceSlice(brokenLow)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("PreviousHigh", prevHighInterface)
	result.AddColumn("PreviousLow", prevLowInterface)
	result.AddColumn("BrokenHigh", brokenHighInterface)
	result.AddColumn("BrokenLow", brokenLowInterface)

	return result, nil
}

// Sessions calculates which candles are within the specified trading session
// This method returns which candles are within the session specified
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - session: the session you want to check (Sydney, Tokyo, London, New York, Asian kill zone, London open kill zone, New York kill zone, london close kill zone, Custom)
// - startTime: the start time of the session in the format "HH:MM" only required for custom session
// - endTime: the end time of the session in the format "HH:MM" only required for custom session
// - timeZone: the time zone of the candles (simplified - assumes UTC for this implementation)
//
// Returns:
// - Active = 1 if the candle is within the session, 0 if not
// - High = the highest point of the session
// - Low = the lowest point of the session
func (smc *SMC) Sessions(ohlc *DataFrame, session, startTime, endTime, timeZone string) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	if session == "Custom" && (startTime == "" || endTime == "") {
		return nil, fmt.Errorf("custom session requires a start and end time")
	}

	_, high, low, _, _ := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)

	// Default session times (in UTC)
	defaultSessions := map[string]map[string]string{
		"Sydney": {
			"start": "21:00",
			"end":   "06:00",
		},
		"Tokyo": {
			"start": "00:00",
			"end":   "09:00",
		},
		"London": {
			"start": "07:00",
			"end":   "16:00",
		},
		"New York": {
			"start": "13:00",
			"end":   "22:00",
		},
		"Asian kill zone": {
			"start": "00:00",
			"end":   "04:00",
		},
		"London open kill zone": {
			"start": "06:00",
			"end":   "09:00",
		},
		"New York kill zone": {
			"start": "11:00",
			"end":   "14:00",
		},
		"london close kill zone": {
			"start": "14:00",
			"end":   "16:00",
		},
		"Custom": {
			"start": startTime,
			"end":   endTime,
		},
	}

	sessionTimes, exists := defaultSessions[session]
	if !exists {
		return nil, fmt.Errorf("unknown session: %s", session)
	}

	// Parse start and end times
	startHour, startMin, err := parseTime(sessionTimes["start"])
	if err != nil {
		return nil, fmt.Errorf("invalid start time: %v", err)
	}

	endHour, endMin, err := parseTime(sessionTimes["end"])
	if err != nil {
		return nil, fmt.Errorf("invalid end time: %v", err)
	}

	startMinutes := startHour*60 + startMin
	endMinutes := endHour*60 + endMin

	active := make([]int, length)
	sessionHigh := make([]float64, length)
	sessionLow := make([]float64, length)

	for i := 0; i < length; i++ {
		// Extract time from OHLC data
		currentTime := ohlc.Data[i].Time
		currentHour := currentTime.Hour()
		currentMinute := currentTime.Minute()
		currentMinutes := currentHour*60 + currentMinute

		// Check if current time is within session
		var inSession bool
		if startMinutes < endMinutes {
			// Session doesn't cross midnight
			inSession = currentMinutes >= startMinutes && currentMinutes <= endMinutes
		} else {
			// Session crosses midnight
			inSession = currentMinutes >= startMinutes || currentMinutes <= endMinutes
		}

		if inSession {
			active[i] = 1
			if i > 0 {
				sessionHigh[i] = math.Max(high[i], sessionHigh[i-1])
				if sessionLow[i-1] != 0 {
					sessionLow[i] = math.Min(low[i], sessionLow[i-1])
				} else {
					sessionLow[i] = low[i]
				}
			} else {
				sessionHigh[i] = high[i]
				sessionLow[i] = low[i]
			}
		} else {
			active[i] = 0
			sessionHigh[i] = 0
			sessionLow[i] = 0
		}
	}

	// Convert to interface slices for Series
	activeInterface := toInterfaceSlice(active)
	highInterface := toInterfaceSlice(sessionHigh)
	lowInterface := toInterfaceSlice(sessionLow)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("Active", activeInterface)
	result.AddColumn("High", highInterface)
	result.AddColumn("Low", lowInterface)

	return result, nil
}

// parseTime parses time string in HH:MM format
func parseTime(timeStr string) (int, int, error) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid time format: %s", timeStr)
	}

	hour, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, 0, fmt.Errorf("invalid hour: %s", parts[0])
	}

	minute, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, 0, fmt.Errorf("invalid minute: %s", parts[1])
	}

	if hour < 0 || hour > 23 || minute < 0 || minute > 59 {
		return 0, 0, fmt.Errorf("time out of range: %s", timeStr)
	}

	return hour, minute, nil
}

// Retracements calculates retracement percentages from swing highs and lows
// This method returns the percentage of a retracement from the swing high or low
//
// Parameters:
// - ohlc: DataFrame containing OHLC data
// - swingHighsLows: DataFrame from the SwingHighsLows function
//
// Returns:
// - Direction = 1 if bullish retracement, -1 if bearish retracement
// - CurrentRetracement% = the current retracement percentage from the swing high or low
// - DeepestRetracement% = the deepest retracement percentage from the swing high or low
func (smc *SMC) Retracements(ohlc *DataFrame, swingHighsLows *Series) (*Series, error) {
	if err := validateOHLC(ohlc); err != nil {
		return nil, err
	}

	_, high, low, _, _ := extractOHLCArrays(ohlc)
	length := len(ohlc.Data)

	// Get swing highs and lows data
	swingHL := swingHighsLows.GetFloat64Column("HighLow")
	swingLevel := swingHighsLows.GetFloat64Column("Level")

	if swingHL == nil || swingLevel == nil {
		return nil, fmt.Errorf("swing highs lows data is missing required columns")
	}

	direction := make([]int, length)
	currentRetracement := make([]float64, length)
	deepestRetracement := make([]float64, length)

	top := 0.0
	bottom := 0.0

	for i := 0; i < length; i++ {
		if !math.IsNaN(swingHL[i]) {
			if swingHL[i] == 1 {
				direction[i] = 1
				top = swingLevel[i]
			} else if swingHL[i] == -1 {
				direction[i] = -1
				bottom = swingLevel[i]
			}
		} else {
			if i > 0 {
				direction[i] = direction[i-1]
			} else {
				direction[i] = 0
			}
		}

		if i > 0 && direction[i-1] == 1 {
			if top != bottom {
				currentRetracement[i] = math.Round((100.0-(((low[i]-bottom)/(top-bottom))*100.0))*10) / 10
			} else {
				currentRetracement[i] = 0
			}

			var prevDeepest float64
			if i > 0 && direction[i-1] == 1 {
				prevDeepest = deepestRetracement[i-1]
			} else {
				prevDeepest = 0
			}
			deepestRetracement[i] = math.Max(prevDeepest, currentRetracement[i])
		}

		if direction[i] == -1 {
			if bottom != top {
				currentRetracement[i] = math.Round((100.0-((high[i]-top)/(bottom-top))*100.0)*10) / 10
			} else {
				currentRetracement[i] = 0
			}

			var prevDeepest float64
			if i > 0 && direction[i-1] == -1 {
				prevDeepest = deepestRetracement[i-1]
			} else {
				prevDeepest = 0
			}
			deepestRetracement[i] = math.Max(prevDeepest, currentRetracement[i])
		}
	}

	// Shift arrays by 1 (equivalent to np.roll(array, 1))
	if length > 0 {
		// Shift current retracement
		tempCurrent := currentRetracement[length-1]
		for i := length - 1; i > 0; i-- {
			currentRetracement[i] = currentRetracement[i-1]
		}
		currentRetracement[0] = tempCurrent

		// Shift deepest retracement
		tempDeepest := deepestRetracement[length-1]
		for i := length - 1; i > 0; i-- {
			deepestRetracement[i] = deepestRetracement[i-1]
		}
		deepestRetracement[0] = tempDeepest

		// Shift direction
		tempDirection := direction[length-1]
		for i := length - 1; i > 0; i-- {
			direction[i] = direction[i-1]
		}
		direction[0] = tempDirection
	}

	// Remove the first 3 retracements as they get calculated incorrectly due to not enough data
	removeFirstCount := 0
	for i := 0; i < length-1; i++ {
		if direction[i] != direction[i+1] {
			removeFirstCount++
		}
		direction[i] = 0
		currentRetracement[i] = 0
		deepestRetracement[i] = 0
		if removeFirstCount == 3 {
			if i+1 < length {
				direction[i+1] = 0
				currentRetracement[i+1] = 0
				deepestRetracement[i+1] = 0
			}
			break
		}
	}

	// Convert to interface slices for Series
	directionInterface := toInterfaceSlice(direction)
	currentInterface := toInterfaceSlice(currentRetracement)
	deepestInterface := toInterfaceSlice(deepestRetracement)

	// Create result series
	result := NewSeries(length)
	result.AddColumn("Direction", directionInterface)
	result.AddColumn("CurrentRetracement%", currentInterface)
	result.AddColumn("DeepestRetracement%", deepestInterface)

	return result, nil
}
