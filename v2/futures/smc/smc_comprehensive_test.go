package smc

import (
	"fmt"
	"math"
	"testing"
	"time"
)

// generateRealisticOHLCData creates realistic OHLC data for testing
func generateRealisticOHLCData() *DataFrame {
	// Simulate realistic price movement starting from $50,000 (like Bitcoin)
	basePrice := 50000.0
	data := make([]OHLC, 100)

	for i := 0; i < 100; i++ {
		// Create realistic price movement with some volatility
		open := basePrice
		if i > 0 {
			open = data[i-1].Close
		}

		// Simulate price movement with some randomness but trending
		var priceChange float64
		switch {
		case i < 20: // Initial uptrend
			priceChange = float64(i)*10 + float64(i%3)*50
		case i < 40: // Correction
			priceChange = -float64(i-20)*15 + float64(i%2)*30
		case i < 60: // Recovery
			priceChange = float64(i-40)*20 + float64(i%4)*40
		case i < 80: // Sideways with volatility
			priceChange = float64((i%8)-4) * 25
		default: // Final push up
			priceChange = float64(i-80)*30 + float64(i%3)*60
		}

		close := open + priceChange
		high := math.Max(open, close) + float64(i%5)*20 + 50
		low := math.Min(open, close) - float64(i%3)*15 - 30
		volume := 1000000 + float64(i%10)*100000

		data[i] = OHLC{
			Open:   open,
			High:   high,
			Low:    low,
			Close:  close,
			Volume: volume,
			Time:   time.Now().Add(time.Duration(i) * time.Hour),
		}
	}

	return &DataFrame{Data: data}
}

// Test data with clear FVG pattern
func generateFVGTestData() *DataFrame {
	return &DataFrame{
		Data: []OHLC{
			{Open: 100, High: 105, Low: 99, Close: 103, Volume: 1000, Time: time.Now()},  // Candle 0
			{Open: 103, High: 110, Low: 104, Close: 108, Volume: 1200, Time: time.Now()}, // Candle 1 - bullish
			{Open: 112, High: 118, Low: 111, Close: 115, Volume: 1100, Time: time.Now()}, // Candle 2 - gap (FVG: 105 < 111)
			{Open: 115, High: 120, Low: 114, Close: 117, Volume: 1050, Time: time.Now()}, // Candle 3
			{Open: 117, High: 122, Low: 116, Close: 119, Volume: 1080, Time: time.Now()}, // Candle 4
			{Open: 119, High: 125, Low: 107, Close: 109, Volume: 1300, Time: time.Now()}, // Candle 5 - mitigates FVG
		},
	}
}

func TestComprehensiveSMCFunctionality(t *testing.T) {
	smc := NewSMC()
	ohlc := generateRealisticOHLCData()

	t.Run("FVG Analysis", func(t *testing.T) {
		result, err := smc.FVG(ohlc, false)
		if err != nil {
			t.Fatalf("FVG analysis failed: %v", err)
		}

		fvgData := result.GetFloat64Column("FVG")
		topData := result.GetFloat64Column("Top")
		bottomData := result.GetFloat64Column("Bottom")
		mitigatedData := result.GetFloat64Column("MitigatedIndex")

		if len(fvgData) != len(ohlc.Data) {
			t.Errorf("FVG data length mismatch: got %d, expected %d", len(fvgData), len(ohlc.Data))
		}

		// Count FVGs found
		fvgCount := 0
		for i, val := range fvgData {
			if !math.IsNaN(val) {
				fvgCount++
				if math.IsNaN(topData[i]) || math.IsNaN(bottomData[i]) {
					t.Errorf("FVG at index %d has invalid top/bottom data", i)
				}
				t.Logf("FVG found at index %d: type=%v, top=%v, bottom=%v, mitigated=%v",
					i, val, topData[i], bottomData[i], mitigatedData[i])
			}
		}
		t.Logf("Total FVGs found: %d", fvgCount)
	})

	t.Run("Swing Highs and Lows", func(t *testing.T) {
		swingLength := 10
		result, err := smc.SwingHighsLows(ohlc, swingLength)
		if err != nil {
			t.Fatalf("SwingHighsLows analysis failed: %v", err)
		}

		hlData := result.GetFloat64Column("HighLow")
		levelData := result.GetFloat64Column("Level")

		swingCount := 0
		for i, val := range hlData {
			if !math.IsNaN(val) {
				swingCount++
				if math.IsNaN(levelData[i]) {
					t.Errorf("Swing at index %d has invalid level data", i)
				}
				t.Logf("Swing found at index %d: type=%v, level=%v", i, val, levelData[i])
			}
		}
		t.Logf("Total swing points found: %d", swingCount)

		if swingCount == 0 {
			t.Error("No swing points found - this is unexpected with realistic data")
		}
	})
}

func TestFVGWithClearPattern(t *testing.T) {
	smc := NewSMC()
	ohlc := generateFVGTestData()

	t.Run("FVG Detection", func(t *testing.T) {
		result, err := smc.FVG(ohlc, false)
		if err != nil {
			t.Fatalf("FVG analysis failed: %v", err)
		}

		fvgData := result.GetFloat64Column("FVG")
		topData := result.GetFloat64Column("Top")
		bottomData := result.GetFloat64Column("Bottom")

		// Should detect bullish FVG(s) in the test data
		bullishFound := 0
		bearishFound := 0
		for i, val := range fvgData {
			if !math.IsNaN(val) {
				if val == 1 {
					bullishFound++
					t.Logf("Bullish FVG found at index %d: top=%v, bottom=%v", i, topData[i], bottomData[i])
				} else if val == -1 {
					bearishFound++
					t.Logf("Bearish FVG found at index %d: top=%v, bottom=%v", i, topData[i], bottomData[i])
				}

				// Validate that FVG has valid levels
				if math.IsNaN(topData[i]) || math.IsNaN(bottomData[i]) {
					t.Errorf("FVG at index %d has invalid levels", i)
				}
				if topData[i] <= bottomData[i] {
					t.Errorf("FVG at index %d has invalid levels: top=%v should be > bottom=%v", i, topData[i], bottomData[i])
				}
			}
		}

		t.Logf("Total FVGs detected: %d bullish, %d bearish", bullishFound, bearishFound)

		if bullishFound == 0 {
			t.Error("Expected to find at least one bullish FVG in test data")
		}
	})

	t.Run("FVG with Consecutive Joining", func(t *testing.T) {
		result, err := smc.FVG(ohlc, true)
		if err != nil {
			t.Fatalf("FVG analysis with joining failed: %v", err)
		}

		fvgData := result.GetFloat64Column("FVG")
		count := 0
		for _, val := range fvgData {
			if !math.IsNaN(val) {
				count++
			}
		}
		t.Logf("FVGs found with consecutive joining: %d", count)
	})
}

func TestSMCIntegrationWorkflow(t *testing.T) {
	smc := NewSMC()
	ohlc := generateRealisticOHLCData()

	t.Run("Complete SMC Analysis Workflow", func(t *testing.T) {
		// Step 1: Find swing highs and lows
		swings, err := smc.SwingHighsLows(ohlc, 5)
		if err != nil {
			t.Fatalf("SwingHighsLows failed: %v", err)
		}

		// Step 2: Analyze market structure (BOS/CHoCH)
		bosChoch, err := smc.BosChoch(ohlc, swings, true)
		if err != nil {
			t.Fatalf("BosChoch failed: %v", err)
		}

		// Step 3: Find order blocks
		orderBlocks, err := smc.OB(ohlc, swings, false)
		if err != nil {
			t.Fatalf("Order Blocks analysis failed: %v", err)
		}

		// Step 4: Identify liquidity zones
		liquidity, err := smc.Liquidity(ohlc, swings, 0.01)
		if err != nil {
			t.Fatalf("Liquidity analysis failed: %v", err)
		}

		// Step 5: Calculate retracements
		retracements, err := smc.Retracements(ohlc, swings)
		if err != nil {
			t.Fatalf("Retracements analysis failed: %v", err)
		}

		// Validate we got results
		bosData := bosChoch.GetFloat64Column("BOS")
		chochData := bosChoch.GetFloat64Column("CHOCH")
		obData := orderBlocks.GetFloat64Column("OB")
		liqData := liquidity.GetFloat64Column("Liquidity")
		retData := retracements.GetIntColumn("Direction")

		t.Logf("Analysis Results:")
		t.Logf("- BOS signals: %d", countNonNaN(bosData))
		t.Logf("- CHoCH signals: %d", countNonNaN(chochData))
		t.Logf("- Order Blocks: %d", countNonNaN(obData))
		t.Logf("- Liquidity zones: %d", countNonNaN(liqData))
		t.Logf("- Retracement periods: %d", countNonZero(retData))

		// All arrays should have the same length
		expectedLen := len(ohlc.Data)
		if len(bosData) != expectedLen || len(obData) != expectedLen {
			t.Error("Result arrays have inconsistent lengths")
		}
	})
}

func TestSessionsAnalysis(t *testing.T) {
	smc := NewSMC()
	ohlc := generateRealisticOHLCData()

	sessions := []string{"London", "New York", "Tokyo", "Sydney"}

	for _, session := range sessions {
		t.Run(fmt.Sprintf("Session_%s", session), func(t *testing.T) {
			result, err := smc.Sessions(ohlc, session, "", "", "UTC")
			if err != nil {
				t.Fatalf("Sessions analysis failed for %s: %v", session, err)
			}

			activeData := result.GetIntColumn("Active")
			highData := result.GetFloat64Column("High")
			lowData := result.GetFloat64Column("Low")

			activeCount := 0
			for _, val := range activeData {
				if val == 1 {
					activeCount++
				}
			}

			t.Logf("%s session: %d active periods found", session, activeCount)

			if len(activeData) != len(ohlc.Data) {
				t.Errorf("Session data length mismatch for %s", session)
			}

			// Validate that high/low data is consistent
			for i, active := range activeData {
				if active == 1 {
					if math.IsNaN(highData[i]) || math.IsNaN(lowData[i]) {
						t.Errorf("Invalid high/low data at active session index %d", i)
					}
				}
			}
		})
	}
}

func TestCustomSession(t *testing.T) {
	smc := NewSMC()
	ohlc := generateRealisticOHLCData()

	result, err := smc.Sessions(ohlc, "Custom", "09:00", "17:00", "UTC")
	if err != nil {
		t.Fatalf("Custom session analysis failed: %v", err)
	}

	activeData := result.GetIntColumn("Active")
	activeCount := 0
	for _, val := range activeData {
		if val == 1 {
			activeCount++
		}
	}

	t.Logf("Custom session (09:00-17:00): %d active periods", activeCount)
}

func TestPreviousHighLow(t *testing.T) {
	smc := NewSMC()
	ohlc := generateRealisticOHLCData()

	timeframes := []string{"1H", "4H", "1D"}

	for _, tf := range timeframes {
		t.Run(fmt.Sprintf("Timeframe_%s", tf), func(t *testing.T) {
			result, err := smc.PreviousHighLow(ohlc, tf)
			if err != nil {
				t.Fatalf("PreviousHighLow analysis failed for %s: %v", tf, err)
			}

			prevHighData := result.GetFloat64Column("PreviousHigh")
			prevLowData := result.GetFloat64Column("PreviousLow")
			brokenHighData := result.GetIntColumn("BrokenHigh")
			brokenLowData := result.GetIntColumn("BrokenLow")

			validDataCount := 0
			for i := range prevHighData {
				if !math.IsNaN(prevHighData[i]) && !math.IsNaN(prevLowData[i]) {
					validDataCount++
				}
			}

			brokenHighCount := countNonZero(brokenHighData)
			brokenLowCount := countNonZero(brokenLowData)

			t.Logf("%s timeframe: %d valid periods, %d broken highs, %d broken lows",
				tf, validDataCount, brokenHighCount, brokenLowCount)
		})
	}
}

func TestErrorHandling(t *testing.T) {
	smc := NewSMC()

	t.Run("Empty DataFrame", func(t *testing.T) {
		emptyOHLC := &DataFrame{Data: []OHLC{}}
		_, err := smc.FVG(emptyOHLC, false)
		if err == nil {
			t.Error("Expected error for empty DataFrame")
		}
	})

	t.Run("Nil DataFrame", func(t *testing.T) {
		_, err := smc.FVG(nil, false)
		if err == nil {
			t.Error("Expected error for nil DataFrame")
		}
	})

	t.Run("Invalid Custom Session", func(t *testing.T) {
		ohlc := generateRealisticOHLCData()
		_, err := smc.Sessions(ohlc, "Custom", "", "", "UTC")
		if err == nil {
			t.Error("Expected error for custom session without start/end time")
		}
	})
}

func TestDataIntegrity(t *testing.T) {
	ohlc := generateRealisticOHLCData()

	t.Run("OHLC Data Validation", func(t *testing.T) {
		for i, candle := range ohlc.Data {
			// High should be >= Open, Close, Low
			if candle.High < candle.Open || candle.High < candle.Close || candle.High < candle.Low {
				t.Errorf("Invalid high at index %d: %v", i, candle)
			}

			// Low should be <= Open, Close, High
			if candle.Low > candle.Open || candle.Low > candle.Close || candle.Low > candle.High {
				t.Errorf("Invalid low at index %d: %v", i, candle)
			}

			// Volume should be positive
			if candle.Volume <= 0 {
				t.Errorf("Invalid volume at index %d: %v", i, candle.Volume)
			}
		}
	})
}

// Helper functions
func countNonNaN(data []float64) int {
	count := 0
	for _, val := range data {
		if !math.IsNaN(val) {
			count++
		}
	}
	return count
}

func countNonZero(data []int) int {
	count := 0
	for _, val := range data {
		if val != 0 {
			count++
		}
	}
	return count
}

// Benchmark test for performance
func BenchmarkSMCAnalysis(b *testing.B) {
	smc := NewSMC()
	ohlc := generateRealisticOHLCData()

	b.Run("FVG", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _ = smc.FVG(ohlc, false)
		}
	})

	b.Run("SwingHighsLows", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _ = smc.SwingHighsLows(ohlc, 10)
		}
	})

	b.Run("CompleteWorkflow", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			swings, _ := smc.SwingHighsLows(ohlc, 5)
			_, _ = smc.BosChoch(ohlc, swings, true)
			_, _ = smc.OB(ohlc, swings, false)
			_, _ = smc.Liquidity(ohlc, swings, 0.01)
		}
	})
}

func Example() {
	// Create SMC analyzer
	smc := NewSMC()

	// Generate sample data (in real usage, this would be your market data)
	ohlc := generateRealisticOHLCData()

	// 1. Find Fair Value Gaps
	fvg, _ := smc.FVG(ohlc, false)
	fvgData := fvg.GetFloat64Column("FVG")
	fmt.Printf("Fair Value Gaps found: %d\n", countNonNaN(fvgData))

	// 2. Identify Swing Highs and Lows
	swings, _ := smc.SwingHighsLows(ohlc, 10)
	swingData := swings.GetFloat64Column("HighLow")
	fmt.Printf("Swing points found: %d\n", countNonNaN(swingData))

	// 3. Analyze market structure changes
	bosChoch, _ := smc.BosChoch(ohlc, swings, true)
	bosData := bosChoch.GetFloat64Column("BOS")
	chochData := bosChoch.GetFloat64Column("CHOCH")
	fmt.Printf("BOS signals: %d, CHoCH signals: %d\n", countNonNaN(bosData), countNonNaN(chochData))

	// 4. Find Order Blocks
	orderBlocks, _ := smc.OB(ohlc, swings, false)
	obData := orderBlocks.GetFloat64Column("OB")
	fmt.Printf("Order Blocks found: %d\n", countNonNaN(obData))

	// 5. Identify Liquidity zones
	liquidity, _ := smc.Liquidity(ohlc, swings, 0.01)
	liqData := liquidity.GetFloat64Column("Liquidity")
	fmt.Printf("Liquidity zones found: %d\n", countNonNaN(liqData))

	// Output:
	// Fair Value Gaps found: 51
	// Swing points found: 6
	// BOS signals: 1, CHoCH signals: 0
	// Order Blocks found: 2
	// Liquidity zones found: 0
}
